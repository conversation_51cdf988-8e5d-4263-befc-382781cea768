import { defineStore } from 'pinia'
import type { Music, PlayerState } from '~/types'

export const usePlayerStore = defineStore('player', () => {
  // 播放器状态
  const state = ref<PlayerState>({
    currentTrack: null,
    isPlaying: false,
    volume: 0.8,
    currentTime: 0,
    duration: 0,
    queue: [],
    currentIndex: -1,
    shuffle: false,
    repeat: 'none'
  })

  // Getters
  const hasCurrentTrack = computed(() => state.value.currentTrack !== null)
  const hasQueue = computed(() => state.value.queue.length > 0)
  const canPlayNext = computed(() => {
    if (state.value.repeat === 'all') return true
    return state.value.currentIndex < state.value.queue.length - 1
  })
  const canPlayPrevious = computed(() => {
    if (state.value.repeat === 'all') return true
    return state.value.currentIndex > 0
  })

  // Actions
  const setCurrentTrack = (track: Music | null) => {
    state.value.currentTrack = track
  }

  const setPlaying = (playing: boolean) => {
    state.value.isPlaying = playing
  }

  const setVolume = (volume: number) => {
    state.value.volume = Math.max(0, Math.min(1, volume))
  }

  const setCurrentTime = (time: number) => {
    state.value.currentTime = time
  }

  const setDuration = (duration: number) => {
    state.value.duration = duration
  }

  const setQueue = (queue: Music[], startIndex: number = 0, shuffle: boolean = false) => {
    state.value.queue = queue
    state.value.shuffle = shuffle

    if (queue.length > 0) {
      const index = Math.max(0, Math.min(startIndex, queue.length - 1))
      setCurrentIndex(index)
      state.value.isPlaying = true
    }
  }

  const addToQueue = (track: Music) => {
    state.value.queue.push(track)
  }

  const removeFromQueue = (index: number) => {
    if (index >= 0 && index < state.value.queue.length) {
      state.value.queue.splice(index, 1)
      // 如果删除的是当前播放的歌曲之前的歌曲，需要调整当前索引
      if (index < state.value.currentIndex) {
        state.value.currentIndex--
      }
      // 如果删除的是当前播放的歌曲，停止播放
      else if (index === state.value.currentIndex) {
        state.value.currentTrack = null
        state.value.isPlaying = false
        state.value.currentIndex = -1
      }
    }
  }

  const setCurrentIndex = (index: number) => {
    if (index >= 0 && index < state.value.queue.length) {
      state.value.currentIndex = index
      state.value.currentTrack = state.value.queue[index]
    }
  }

  const toggleShuffle = () => {
    state.value.shuffle = !state.value.shuffle
  }

  const setRepeat = (mode: 'none' | 'one' | 'all') => {
    state.value.repeat = mode
  }

  const playTrack = (track: Music, queue?: Music[]) => {
    if (queue) {
      state.value.queue = queue
      state.value.currentIndex = queue.findIndex(t => t.id === track.id)
    } else {
      // 如果没有提供队列，将当前歌曲作为队列
      state.value.queue = [track]
      state.value.currentIndex = 0
    }
    state.value.currentTrack = track
    state.value.isPlaying = true
  }

  const playNext = () => {
    if (!hasQueue.value) return

    let nextIndex = state.value.currentIndex + 1

    if (state.value.shuffle) {
      // 随机播放模式
      const availableIndices = state.value.queue
        .map((_, index) => index)
        .filter(index => index !== state.value.currentIndex)
      
      if (availableIndices.length > 0) {
        nextIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)]
      }
    } else if (nextIndex >= state.value.queue.length) {
      // 到达队列末尾
      if (state.value.repeat === 'all') {
        nextIndex = 0
      } else {
        return // 不能播放下一首
      }
    }

    setCurrentIndex(nextIndex)
    state.value.isPlaying = true
  }

  const playPrevious = () => {
    if (!hasQueue.value) return

    let prevIndex = state.value.currentIndex - 1

    if (prevIndex < 0) {
      if (state.value.repeat === 'all') {
        prevIndex = state.value.queue.length - 1
      } else {
        return // 不能播放上一首
      }
    }

    setCurrentIndex(prevIndex)
    state.value.isPlaying = true
  }

  const togglePlay = () => {
    state.value.isPlaying = !state.value.isPlaying
  }

  const stop = () => {
    state.value.isPlaying = false
    state.value.currentTime = 0
  }

  const clearQueue = () => {
    state.value.queue = []
    state.value.currentIndex = -1
    state.value.currentTrack = null
    state.value.isPlaying = false
  }

  return {
    // State
    state: readonly(state),
    
    // Getters
    hasCurrentTrack,
    hasQueue,
    canPlayNext,
    canPlayPrevious,
    
    // Actions
    setCurrentTrack,
    setPlaying,
    setVolume,
    setCurrentTime,
    setDuration,
    setQueue,
    addToQueue,
    removeFromQueue,
    setCurrentIndex,
    toggleShuffle,
    setRepeat,
    playTrack,
    playNext,
    playPrevious,
    togglePlay,
    stop,
    clearQueue
  }
})
