<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 页面头部 -->
    <div class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              发现歌单
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              探索精彩的音乐收藏
            </p>
          </div>
          
          <!-- 筛选选项 -->
          <div class="flex items-center gap-4">
            <select
              v-model="selectedGenre"
              @change="handleGenreChange"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">所有类型</option>
              <option value="pop">流行</option>
              <option value="rock">摇滚</option>
              <option value="jazz">爵士</option>
              <option value="classical">古典</option>
              <option value="electronic">电子</option>
              <option value="folk">民谣</option>
            </select>
            
            <select
              v-model="timeRange"
              @change="handleTimeRangeChange"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="day">今日热门</option>
              <option value="week">本周热门</option>
              <option value="month">本月热门</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-12">
      <!-- 推荐歌单 -->
      <section>
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
            为您推荐
          </h2>
          <NuxtLink
            to="/playlists/recommended"
            class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
          >
            查看更多 →
          </NuxtLink>
        </div>
        
        <PlaylistGrid
          :playlists="recommendedPlaylists"
          :loading="loadingRecommended"
          :show-actions="false"
          :show-create-button="false"
          :skeleton-count="4"
          empty-title="暂无推荐歌单"
          empty-description="系统正在为您准备个性化推荐"
          empty-icon="sparkles"
          @play="handlePlay"
          @like="handleLike"
        />
      </section>

      <!-- 热门歌单 -->
      <section>
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ timeRangeText }}热门
          </h2>
          <NuxtLink
            to="/playlists/trending"
            class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
          >
            查看更多 →
          </NuxtLink>
        </div>
        
        <PlaylistGrid
          :playlists="trendingPlaylists"
          :loading="loadingTrending"
          :show-actions="false"
          :show-create-button="false"
          :skeleton-count="8"
          empty-title="暂无热门歌单"
          empty-description="还没有热门歌单数据"
          empty-icon="fire"
          @play="handlePlay"
          @like="handleLike"
        />
      </section>

      <!-- 分类歌单 -->
      <section v-if="selectedGenre">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ genreText }}歌单
          </h2>
        </div>
        
        <PlaylistGrid
          :playlists="genrePlaylists"
          :loading="loadingGenre"
          :show-actions="false"
          :show-create-button="false"
          :show-load-more="hasMoreGenre"
          :loading-more="loadingMoreGenre"
          empty-title="暂无相关歌单"
          empty-description="该分类下还没有歌单"
          empty-icon="musical-note"
          @play="handlePlay"
          @like="handleLike"
          @load-more="loadMoreGenre"
        />
      </section>

      <!-- 最新歌单 -->
      <section>
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
            最新歌单
          </h2>
          <NuxtLink
            to="/playlists?sortBy=latest"
            class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
          >
            查看更多 →
          </NuxtLink>
        </div>
        
        <PlaylistGrid
          :playlists="latestPlaylists"
          :loading="loadingLatest"
          :show-actions="false"
          :show-create-button="false"
          :skeleton-count="8"
          empty-title="暂无最新歌单"
          empty-description="还没有最新歌单"
          empty-icon="clock"
          @play="handlePlay"
          @like="handleLike"
        />
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

// 页面元数据
definePageMeta({
  title: '发现歌单'
})

// 响应式数据
const recommendedPlaylists = ref<Playlist[]>([])
const trendingPlaylists = ref<Playlist[]>([])
const genrePlaylists = ref<Playlist[]>([])
const latestPlaylists = ref<Playlist[]>([])

const loadingRecommended = ref(true)
const loadingTrending = ref(true)
const loadingGenre = ref(false)
const loadingLatest = ref(true)
const loadingMoreGenre = ref(false)

const selectedGenre = ref('')
const timeRange = ref<'day' | 'week' | 'month'>('week')
const genrePage = ref(1)
const hasMoreGenre = ref(false)

// Composables
const { getRecommendedPlaylists, getTrendingPlaylists, getPlaylists } = usePlaylistApi()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()
const playerStore = usePlayerStore()

// 计算属性
const timeRangeText = computed(() => {
  switch (timeRange.value) {
    case 'day': return '今日'
    case 'week': return '本周'
    case 'month': return '本月'
    default: return '本周'
  }
})

const genreText = computed(() => {
  const genreMap: Record<string, string> = {
    pop: '流行',
    rock: '摇滚',
    jazz: '爵士',
    classical: '古典',
    electronic: '电子',
    folk: '民谣'
  }
  return genreMap[selectedGenre.value] || selectedGenre.value
})

// 获取推荐歌单
const fetchRecommendedPlaylists = async () => {
  try {
    loadingRecommended.value = true
    const response = await getRecommendedPlaylists({ limit: 4 })
    recommendedPlaylists.value = response.data
  } catch (error) {
    handleError(error, '获取推荐歌单失败')
  } finally {
    loadingRecommended.value = false
  }
}

// 获取热门歌单
const fetchTrendingPlaylists = async () => {
  try {
    loadingTrending.value = true
    const response = await getTrendingPlaylists({ 
      period: timeRange.value,
      limit: 8 
    })
    trendingPlaylists.value = response.data
  } catch (error) {
    handleError(error, '获取热门歌单失败')
  } finally {
    loadingTrending.value = false
  }
}

// 获取分类歌单
const fetchGenrePlaylists = async (loadMore = false) => {
  if (!selectedGenre.value) return

  try {
    if (loadMore) {
      loadingMoreGenre.value = true
    } else {
      loadingGenre.value = true
      genrePage.value = 1
    }

    const response = await getPlaylists({
      page: genrePage.value,
      limit: 8,
      genre: selectedGenre.value,
      isPublic: true,
      sortBy: 'popular'
    })

    if (loadMore) {
      genrePlaylists.value.push(...response.data.data)
    } else {
      genrePlaylists.value = response.data.data
    }

    hasMoreGenre.value = genrePage.value < Math.ceil(response.data.total / 8)
  } catch (error) {
    handleError(error, '获取分类歌单失败')
  } finally {
    loadingGenre.value = false
    loadingMoreGenre.value = false
  }
}

// 获取最新歌单
const fetchLatestPlaylists = async () => {
  try {
    loadingLatest.value = true
    const response = await getPlaylists({
      limit: 8,
      isPublic: true,
      sortBy: 'latest'
    })
    latestPlaylists.value = response.data.data
  } catch (error) {
    handleError(error, '获取最新歌单失败')
  } finally {
    loadingLatest.value = false
  }
}

// 处理类型变化
const handleGenreChange = () => {
  if (selectedGenre.value) {
    fetchGenrePlaylists()
  } else {
    genrePlaylists.value = []
  }
}

// 处理时间范围变化
const handleTimeRangeChange = () => {
  fetchTrendingPlaylists()
}

// 加载更多分类歌单
const loadMoreGenre = () => {
  genrePage.value++
  fetchGenrePlaylists(true)
}

// 播放歌单
const handlePlay = (playlist: Playlist) => {
  if (playlist.songs?.length) {
    playerStore.setQueue(playlist.songs, 0)
    showNotification(`开始播放歌单: ${playlist.name}`, 'success')
  } else {
    showNotification('歌单暂无歌曲', 'warning')
  }
}

// 点赞歌单
const handleLike = (playlist: Playlist) => {
  // 点赞逻辑已在 PlaylistCard 组件中处理
}

// 页面加载时获取数据
onMounted(() => {
  fetchRecommendedPlaylists()
  fetchTrendingPlaylists()
  fetchLatestPlaylists()
})
</script>
