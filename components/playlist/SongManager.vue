<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
    <!-- 头部 -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            歌曲列表
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ songs.length }} 首歌曲 · 总时长 {{ formatTotalDuration }}
          </p>
        </div>
        
        <div class="flex items-center gap-3">
          <!-- 播放模式切换 -->
          <div class="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              @click="playMode = 'order'"
              :class="[
                'px-3 py-1 text-sm rounded-md transition-colors',
                playMode === 'order' 
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              顺序播放
            </button>
            <button
              @click="playMode = 'shuffle'"
              :class="[
                'px-3 py-1 text-sm rounded-md transition-colors',
                playMode === 'shuffle' 
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              随机播放
            </button>
          </div>

          <!-- 操作按钮 -->
          <UiButton
            v-if="canEdit"
            @click="$emit('add-songs')"
            variant="outline"
            size="sm"
          >
            <UiIcon name="plus" class="w-4 h-4" />
            添加歌曲
          </UiButton>
          
          <UiButton
            @click="handlePlayAll"
            variant="primary"
            size="sm"
            :disabled="songs.length === 0"
          >
            <UiIcon name="play" class="w-4 h-4" />
            播放全部
          </UiButton>
        </div>
      </div>
    </div>

    <!-- 歌曲列表 -->
    <div v-if="songs.length > 0">
      <!-- 列表头部 -->
      <div class="px-6 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
        <div class="flex items-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          <div class="w-8"></div>
          <div class="flex-1 min-w-0">歌曲</div>
          <div class="w-20 text-center">时长</div>
          <div class="w-24 text-center">播放次数</div>
          <div class="w-16"></div>
        </div>
      </div>

      <!-- 可拖拽的歌曲列表 -->
      <draggable
        v-model="localSongs"
        :disabled="!canEdit"
        item-key="id"
        handle=".drag-handle"
        ghost-class="ghost"
        chosen-class="chosen"
        drag-class="drag"
        @end="handleDragEnd"
        class="divide-y divide-gray-200 dark:divide-gray-700"
      >
        <template #item="{ element: song, index }">
          <div
            :class="[
              'flex items-center px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors group',
              currentSong?.id === song.id && 'bg-primary-50 dark:bg-primary-900/20'
            ]"
          >
            <!-- 拖拽手柄 / 序号 -->
            <div class="w-8 flex items-center justify-center">
              <div
                v-if="canEdit"
                class="drag-handle cursor-move opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <UiIcon name="bars-3" class="w-4 h-4 text-gray-400" />
              </div>
              <span
                v-else
                class="text-sm text-gray-500 dark:text-gray-400"
              >
                {{ index + 1 }}
              </span>
            </div>

            <!-- 歌曲信息 -->
            <div class="flex-1 min-w-0 flex items-center gap-3">
              <!-- 封面 -->
              <div class="relative">
                <img
                  :src="song.coverUrl || '/default-music-cover.jpg'"
                  :alt="song.title"
                  class="w-12 h-12 rounded object-cover"
                />
                <!-- 播放状态指示器 -->
                <div
                  v-if="currentSong?.id === song.id && isPlaying"
                  class="absolute inset-0 bg-black bg-opacity-40 rounded flex items-center justify-center"
                >
                  <div class="w-3 h-3 bg-primary-500 rounded-full animate-pulse"></div>
                </div>
              </div>

              <!-- 歌曲详情 -->
              <div class="min-w-0 flex-1">
                <h4 
                  :class="[
                    'font-medium truncate cursor-pointer hover:text-primary-500 transition-colors',
                    currentSong?.id === song.id 
                      ? 'text-primary-600 dark:text-primary-400' 
                      : 'text-gray-900 dark:text-white'
                  ]"
                  @click="handlePlaySong(song, index)"
                >
                  {{ song.title }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {{ song.artist }}
                  <span v-if="song.album"> · {{ song.album }}</span>
                </p>
              </div>
            </div>

            <!-- 时长 -->
            <div class="w-20 text-center text-sm text-gray-500 dark:text-gray-400">
              {{ formatDuration(song.duration) }}
            </div>

            <!-- 播放次数 -->
            <div class="w-24 text-center text-sm text-gray-500 dark:text-gray-400">
              {{ formatNumber(song.playCount) }}
            </div>

            <!-- 操作按钮 -->
            <div class="w-16 flex items-center justify-end gap-1">
              <!-- 喜欢按钮 -->
              <button
                @click="handleLikeSong(song)"
                :class="[
                  'p-2 rounded-full transition-colors opacity-0 group-hover:opacity-100',
                  song.isLiked 
                    ? 'text-red-500 hover:text-red-600' 
                    : 'text-gray-400 hover:text-red-500'
                ]"
              >
                <UiIcon name="heart" class="w-4 h-4" />
              </button>

              <!-- 更多操作 -->
              <div class="relative">
                <button
                  @click="toggleSongMenu(song.id)"
                  class="p-2 rounded-full text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors opacity-0 group-hover:opacity-100"
                >
                  <UiIcon name="ellipsis-horizontal" class="w-4 h-4" />
                </button>

                <!-- 下拉菜单 -->
                <div
                  v-if="activeSongMenu === song.id"
                  v-click-outside="() => activeSongMenu = null"
                  class="absolute right-0 top-full mt-1 w-40 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-10"
                >
                  <button
                    @click="handleAddToQueue(song)"
                    class="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center gap-2"
                  >
                    <UiIcon name="queue-list" class="w-4 h-4" />
                    添加到队列
                  </button>
                  <button
                    @click="handleAddToPlaylist(song)"
                    class="w-full px-3 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center gap-2"
                  >
                    <UiIcon name="plus" class="w-4 h-4" />
                    添加到歌单
                  </button>
                  <button
                    v-if="canEdit"
                    @click="handleRemoveSong(song)"
                    class="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center gap-2"
                  >
                    <UiIcon name="trash" class="w-4 h-4" />
                    从歌单移除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- 空状态 -->
    <div v-else class="p-12 text-center">
      <UiIcon name="musical-note" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        歌单还没有歌曲
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        添加一些您喜爱的音乐到这个歌单
      </p>
      <UiButton
        v-if="canEdit"
        @click="$emit('add-songs')"
        variant="primary"
      >
        <UiIcon name="plus" class="w-4 h-4" />
        添加歌曲
      </UiButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import draggable from 'vuedraggable'
import type { Music } from '~/types'

interface Props {
  songs: Music[]
  canEdit?: boolean
  currentSong?: Music | null
  isPlaying?: boolean
}

interface Emits {
  (e: 'play-song', song: Music, index: number): void
  (e: 'play-all', mode: 'order' | 'shuffle'): void
  (e: 'remove-song', song: Music): void
  (e: 'reorder-songs', songs: Music[]): void
  (e: 'like-song', song: Music): void
  (e: 'add-to-queue', song: Music): void
  (e: 'add-to-playlist', song: Music): void
  (e: 'add-songs'): void
}

const props = withDefaults(defineProps<Props>(), {
  canEdit: false,
  currentSong: null,
  isPlaying: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const localSongs = ref([...props.songs])
const playMode = ref<'order' | 'shuffle'>('order')
const activeSongMenu = ref<string | null>(null)

// 监听 songs 变化
watch(() => props.songs, (newSongs) => {
  localSongs.value = [...newSongs]
}, { deep: true })

// 计算属性
const formatTotalDuration = computed(() => {
  const totalSeconds = localSongs.value.reduce((sum, song) => sum + song.duration, 0)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours} 小时 ${minutes} 分钟`
  }
  return `${minutes} 分钟`
})

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 播放歌曲
const handlePlaySong = (song: Music, index: number) => {
  emit('play-song', song, index)
}

// 播放全部
const handlePlayAll = () => {
  emit('play-all', playMode.value)
}

// 移除歌曲
const handleRemoveSong = (song: Music) => {
  activeSongMenu.value = null
  emit('remove-song', song)
}

// 拖拽结束处理
const handleDragEnd = () => {
  emit('reorder-songs', localSongs.value)
}

// 喜欢歌曲
const handleLikeSong = (song: Music) => {
  emit('like-song', song)
}

// 添加到队列
const handleAddToQueue = (song: Music) => {
  activeSongMenu.value = null
  emit('add-to-queue', song)
}

// 添加到歌单
const handleAddToPlaylist = (song: Music) => {
  activeSongMenu.value = null
  emit('add-to-playlist', song)
}

// 切换歌曲菜单
const toggleSongMenu = (songId: string) => {
  activeSongMenu.value = activeSongMenu.value === songId ? null : songId
}

// 点击外部关闭菜单
const vClickOutside = {
  mounted(el: HTMLElement, binding: any) {
    el._clickOutside = (event: Event) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value()
      }
    }
    document.addEventListener('click', el._clickOutside)
  },
  unmounted(el: HTMLElement) {
    document.removeEventListener('click', el._clickOutside)
  }
}
</script>

<style scoped>
.ghost {
  opacity: 0.5;
  background: #f3f4f6;
}

.chosen {
  background: #e5e7eb;
}

.drag {
  transform: rotate(5deg);
}

.dark .ghost {
  background: #374151;
}

.dark .chosen {
  background: #4b5563;
}
</style>
