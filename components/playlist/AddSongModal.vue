<template>
  <UiModal
    :show="true"
    size="xl"
    @close="$emit('close')"
  >
    <template #header>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        添加歌曲到歌单
      </h3>
    </template>

    <div class="space-y-6">
      <!-- 搜索框 -->
      <div>
        <UiInput
          v-model="searchQuery"
          placeholder="搜索歌曲、艺术家或专辑..."
          type="search"
          icon="magnifying-glass"
          @input="handleSearch"
        />
      </div>

      <!-- 筛选选项 -->
      <div class="flex flex-wrap gap-4">
        <select
          v-model="filterGenre"
          @change="handleFilter"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        >
          <option value="">所有类型</option>
          <option value="pop">流行</option>
          <option value="rock">摇滚</option>
          <option value="jazz">爵士</option>
          <option value="classical">古典</option>
          <option value="electronic">电子</option>
          <option value="folk">民谣</option>
        </select>

        <select
          v-model="sortBy"
          @change="handleSort"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        >
          <option value="createdAt">最新上传</option>
          <option value="playCount">播放量</option>
          <option value="likeCount">点赞数</option>
          <option value="title">歌曲名称</option>
        </select>
      </div>

      <!-- 已选择的歌曲 -->
      <div v-if="selectedSongs.length > 0" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="font-medium text-gray-900 dark:text-white">
            已选择 {{ selectedSongs.length }} 首歌曲
          </h4>
          <button
            @click="clearSelection"
            class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            清空选择
          </button>
        </div>
        <div class="flex flex-wrap gap-2">
          <div
            v-for="song in selectedSongs"
            :key="song.id"
            class="flex items-center gap-2 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-sm"
          >
            <span class="truncate max-w-32">{{ song.title }}</span>
            <button
              @click="removeSong(song)"
              class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200"
            >
              <UiIcon name="x-mark" class="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      <!-- 歌曲列表 -->
      <div class="max-h-96 overflow-y-auto">
        <!-- 加载状态 -->
        <div v-if="loading" class="space-y-3">
          <div v-for="i in 5" :key="i" class="animate-pulse flex items-center gap-3 p-3">
            <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
              <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
            </div>
            <div class="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
        </div>

        <!-- 歌曲列表 -->
        <div v-else-if="songs.length > 0" class="space-y-1">
          <div
            v-for="song in songs"
            :key="song.id"
            class="flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <!-- 复选框 -->
            <input
              :id="`song-${song.id}`"
              v-model="selectedSongIds"
              :value="song.id"
              type="checkbox"
              class="text-primary-500 focus:ring-primary-500 rounded"
            />

            <!-- 歌曲信息 -->
            <label :for="`song-${song.id}`" class="flex items-center gap-3 flex-1 cursor-pointer">
              <img
                :src="song.coverUrl || '/default-music-cover.jpg'"
                :alt="song.title"
                class="w-12 h-12 rounded object-cover"
              />
              <div class="min-w-0 flex-1">
                <h4 class="font-medium text-gray-900 dark:text-white truncate">
                  {{ song.title }}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {{ song.artist }}
                  <span v-if="song.album"> · {{ song.album }}</span>
                </p>
              </div>
            </label>

            <!-- 时长 -->
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ formatDuration(song.duration) }}
            </div>

            <!-- 播放按钮 -->
            <button
              @click="previewSong(song)"
              class="p-2 text-gray-400 hover:text-primary-500 transition-colors"
            >
              <UiIcon name="play" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-8">
          <UiIcon name="musical-note" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p class="text-gray-600 dark:text-gray-400">
            {{ searchQuery ? '没有找到匹配的歌曲' : '暂无可添加的歌曲' }}
          </p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex justify-center">
        <nav class="flex items-center gap-2">
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
          >
            上一页
          </button>
          
          <span class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
          >
            下一页
          </button>
        </nav>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          已选择 {{ selectedSongs.length }} 首歌曲
        </div>
        <div class="flex items-center gap-3">
          <UiButton
            @click="$emit('close')"
            variant="outline"
          >
            取消
          </UiButton>
          <UiButton
            @click="handleAddSongs"
            variant="primary"
            :loading="adding"
            :disabled="selectedSongs.length === 0 || adding"
          >
            添加到歌单
          </UiButton>
        </div>
      </div>
    </template>
  </UiModal>
</template>

<script setup lang="ts">
import type { Music } from '~/types'

interface Props {
  playlistId: string
}

interface Emits {
  (e: 'close'): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const songs = ref<Music[]>([])
const selectedSongIds = ref<string[]>([])
const loading = ref(true)
const adding = ref(false)
const searchQuery = ref('')
const filterGenre = ref('')
const sortBy = ref('createdAt')
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 20

// Composables
const { addSongsToPlaylist } = usePlaylistApi()
const { getMusic } = useMusicApi()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()

// 计算属性
const selectedSongs = computed(() => {
  return songs.value.filter(song => selectedSongIds.value.includes(song.id))
})

// 获取歌曲列表
const fetchSongs = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize,
      search: searchQuery.value || undefined,
      genre: filterGenre.value || undefined,
      sortBy: sortBy.value === 'createdAt' ? 'latest' :
             sortBy.value === 'playCount' ? 'popular' :
             sortBy.value,
      excludePlaylist: props.playlistId // 排除已在歌单中的歌曲
    }

    const response = await getMusic(params)

    songs.value = response.data.data
    totalPages.value = Math.ceil(response.data.total / pageSize)
  } catch (error) {
    handleError(error, '获取歌曲列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = useDebounceFn(() => {
  currentPage.value = 1
  fetchSongs()
}, 300)

// 筛选处理
const handleFilter = () => {
  currentPage.value = 1
  fetchSongs()
}

// 排序处理
const handleSort = () => {
  currentPage.value = 1
  fetchSongs()
}

// 分页处理
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    fetchSongs()
  }
}

// 清空选择
const clearSelection = () => {
  selectedSongIds.value = []
}

// 移除单个歌曲
const removeSong = (song: Music) => {
  const index = selectedSongIds.value.indexOf(song.id)
  if (index > -1) {
    selectedSongIds.value.splice(index, 1)
  }
}

// 预览歌曲
const previewSong = (song: Music) => {
  // TODO: 集成播放器预览功能
  showNotification(`预览: ${song.title} - ${song.artist}`, 'info')
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 添加歌曲到歌单
const handleAddSongs = async () => {
  if (selectedSongs.value.length === 0) return

  try {
    adding.value = true

    await addSongsToPlaylist(props.playlistId, selectedSongIds.value)

    showNotification(
      `成功添加 ${selectedSongs.value.length} 首歌曲到歌单`,
      'success'
    )

    emit('success')
  } catch (error) {
    handleError(error, '添加歌曲失败')
  } finally {
    adding.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSongs()
})
</script>
