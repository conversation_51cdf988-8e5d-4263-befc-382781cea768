<template>
  <UiModal
    :show="true"
    size="lg"
    @close="$emit('close')"
  >
    <template #header>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        {{ isEditing ? '编辑歌单' : '创建歌单' }}
      </h3>
    </template>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- 歌单封面 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          歌单封面
        </label>
        <div class="flex items-center gap-4">
          <!-- 封面预览 -->
          <div class="relative">
            <img
              :src="coverPreview || '/default-playlist-cover.jpg'"
              alt="歌单封面"
              class="w-24 h-24 rounded-lg object-cover border border-gray-300 dark:border-gray-600"
            />
            <button
              v-if="coverPreview && coverPreview !== '/default-playlist-cover.jpg'"
              @click="removeCover"
              type="button"
              class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
            >
              <UiIcon name="x-mark" class="w-3 h-3" />
            </button>
          </div>

          <!-- 上传按钮 -->
          <div>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileChange"
              class="hidden"
            />
            <UiButton
              @click="$refs.fileInput?.click()"
              type="button"
              variant="outline"
              size="sm"
            >
              <UiIcon name="photo" class="w-4 h-4" />
              选择封面
            </UiButton>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              支持 JPG、PNG 格式，建议尺寸 400x400
            </p>
          </div>
        </div>
      </div>

      <!-- 歌单名称 -->
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          歌单名称 <span class="text-red-500">*</span>
        </label>
        <UiInput
          id="name"
          v-model="form.name"
          placeholder="请输入歌单名称"
          :error="errors.name"
          required
        />
      </div>

      <!-- 歌单描述 -->
      <div>
        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          歌单描述
        </label>
        <textarea
          id="description"
          v-model="form.description"
          rows="4"
          placeholder="请输入歌单描述..."
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          :class="{ 'border-red-500': errors.description }"
        />
        <p v-if="errors.description" class="mt-1 text-sm text-red-600">
          {{ errors.description }}
        </p>
      </div>

      <!-- 隐私设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          隐私设置
        </label>
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              v-model="form.isPublic"
              :value="true"
              type="radio"
              name="privacy"
              class="text-primary-500 focus:ring-primary-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              公开 - 所有人都可以查看和搜索到这个歌单
            </span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.isPublic"
              :value="false"
              type="radio"
              name="privacy"
              class="text-primary-500 focus:ring-primary-500"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              私有 - 只有您可以查看这个歌单
            </span>
          </label>
        </div>
      </div>

      <!-- 表单按钮 -->
      <div class="flex items-center justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <UiButton
          @click="$emit('close')"
          type="button"
          variant="outline"
        >
          取消
        </UiButton>
        <UiButton
          type="submit"
          variant="primary"
          :loading="loading"
          :disabled="!isFormValid"
        >
          {{ isEditing ? '保存更改' : '创建歌单' }}
        </UiButton>
      </div>
    </form>
  </UiModal>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

interface Props {
  playlist?: Playlist
}

interface Emits {
  (e: 'close'): void
  (e: 'success', playlist: Playlist): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const form = reactive({
  name: '',
  description: '',
  isPublic: true
})

const errors = reactive({
  name: '',
  description: ''
})

const loading = ref(false)
const coverFile = ref<File | null>(null)
const coverPreview = ref<string>('')

// Composables
const { createPlaylist, updatePlaylist, uploadPlaylistCover } = usePlaylistApi()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()

// 计算属性
const isEditing = computed(() => !!props.playlist)

const isFormValid = computed(() => {
  return form.name.trim().length > 0 && form.name.trim().length <= 100
})

// 初始化表单数据
const initializeForm = () => {
  if (props.playlist) {
    form.name = props.playlist.name
    form.description = props.playlist.description || ''
    form.isPublic = props.playlist.isPublic
    coverPreview.value = props.playlist.coverUrl || ''
  } else {
    form.name = ''
    form.description = ''
    form.isPublic = true
    coverPreview.value = ''
  }
  
  // 清空错误
  errors.name = ''
  errors.description = ''
}

// 处理文件选择
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    showNotification('请选择图片文件', 'error')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    showNotification('图片文件不能超过 5MB', 'error')
    return
  }

  coverFile.value = file

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    coverPreview.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

// 移除封面
const removeCover = () => {
  coverFile.value = null
  coverPreview.value = '/default-playlist-cover.jpg'
}

// 验证表单
const validateForm = () => {
  let isValid = true

  // 验证名称
  if (!form.name.trim()) {
    errors.name = '请输入歌单名称'
    isValid = false
  } else if (form.name.trim().length > 100) {
    errors.name = '歌单名称不能超过100个字符'
    isValid = false
  } else {
    errors.name = ''
  }

  // 验证描述
  if (form.description && form.description.length > 500) {
    errors.description = '歌单描述不能超过500个字符'
    isValid = false
  } else {
    errors.description = ''
  }

  return isValid
}

// 上传封面
const uploadCover = async (playlistId?: string): Promise<string | null> => {
  if (!coverFile.value) return null

  try {
    if (playlistId) {
      // 如果有歌单ID，使用专门的封面上传接口
      const response = await uploadPlaylistCover(playlistId, coverFile.value)
      return response.data.coverUrl
    } else {
      // 否则使用通用上传接口
      const { upload } = useApi()
      const response = await upload('/upload', coverFile.value, 'playlist-cover')
      return response.data.url
    }
  } catch (error) {
    console.error('封面上传失败:', error)
    return null
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    loading.value = true

    // 上传封面
    let coverUrl = null
    if (coverFile.value) {
      coverUrl = await uploadCover()
      if (!coverUrl) {
        showNotification('封面上传失败，请重试', 'error')
        return
      }
    }

    // 准备提交数据
    const submitData = {
      name: form.name.trim(),
      description: form.description.trim() || undefined,
      isPublic: form.isPublic,
      ...(coverUrl && { coverUrl })
    }

    let response: Playlist

    if (isEditing.value) {
      // 编辑歌单
      response = await updatePlaylist(props.playlist!.id, submitData)

      // 如果有新封面，上传封面
      if (coverFile.value) {
        const newCoverUrl = await uploadCover(props.playlist!.id)
        if (newCoverUrl) {
          response.data.coverUrl = newCoverUrl
        }
      }

      showNotification('歌单更新成功', 'success')
    } else {
      // 创建歌单
      response = await createPlaylist(submitData)

      // 如果有封面，上传封面
      if (coverFile.value && response.data) {
        const newCoverUrl = await uploadCover(response.data.id)
        if (newCoverUrl) {
          response.data.coverUrl = newCoverUrl
        }
      }

      showNotification('歌单创建成功', 'success')
    }

    emit('success', response.data)
  } catch (error) {
    handleError(error, isEditing.value ? '更新歌单失败' : '创建歌单失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时初始化表单
onMounted(() => {
  initializeForm()
})

// 监听 playlist 变化
watch(() => props.playlist, () => {
  initializeForm()
}, { deep: true })
</script>
